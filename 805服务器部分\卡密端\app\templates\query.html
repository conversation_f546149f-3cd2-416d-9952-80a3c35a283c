<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密生成与查询工具 - 网页版</title>
    <!-- 强制使用HTTPS -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css', _external=True) }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css', _external=True) }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap-icons.css', _external=True) }}">
</head>
<body>
    <div class="container mt-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="mb-0">卡密生成与查询工具</h2>
                    <div>
                        <a href="{{ url_for('main.index', _external=True) }}" class="btn btn-outline-light btn-sm me-2">原版生成器</a>
                        <a href="{{ url_for('main.logout', _external=True) }}" class="btn btn-outline-light btn-sm">退出登录</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- 选项卡导航 -->
                <ul class="nav nav-tabs mb-4" id="queryTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="generator-tab" data-bs-toggle="tab" data-bs-target="#generator-content" type="button" role="tab" aria-controls="generator-content" aria-selected="true">密码生成器</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="api-key-tab" data-bs-toggle="tab" data-bs-target="#api-key-content" type="button" role="tab" aria-controls="api-key-content" aria-selected="false">API Key查询</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="cookie-tab" data-bs-toggle="tab" data-bs-target="#cookie-content" type="button" role="tab" aria-controls="cookie-content" aria-selected="false">Cookie查询</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="manage-tab" data-bs-toggle="tab" data-bs-target="#manage-content" type="button" role="tab" aria-controls="manage-content" aria-selected="false">API Key管理</button>
                    </li>
                </ul>
                
                <!-- 选项卡内容 -->
                <div class="tab-content" id="queryTabsContent">
                    <!-- 密码生成器选项卡 -->
                    <div class="tab-pane fade show active" id="generator-content" role="tabpanel" aria-labelledby="generator-tab">
                        <div class="row mb-4">
                            <!-- 输入区域 -->
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="gen_quota" class="form-label">总配额:</label>
                                    <div class="input-group">
                                        <input type="number" id="gen_quota" class="form-control" value="4" min="1" max="1000000">
                                        <div class="btn-group ms-2" role="group">
                                            <button type="button" class="btn btn-outline-primary gen-quick-btn" data-value="4">4</button>
                                            <button type="button" class="btn btn-outline-primary gen-quick-btn" data-value="7">7</button>
                                            <button type="button" class="btn btn-outline-primary gen-quick-btn" data-value="12">12</button>
                                            <button type="button" class="btn btn-outline-primary gen-quick-btn" data-value="65">65</button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="gen_expires" class="form-label">有效期(天):</label>
                                    <div class="input-group">
                                        <input type="number" id="gen_expires" class="form-control" value="1" min="0" max="3650">
                                        <div class="btn-group ms-2" role="group">
                                            <button type="button" class="btn btn-outline-primary gen-quick-btn-expires" data-value="1">1天</button>
                                            <button type="button" class="btn btn-outline-primary gen-quick-btn-expires" data-value="3">3天</button>
                                            <button type="button" class="btn btn-outline-primary gen-quick-btn-expires" data-value="7">7天</button>
                                            <button type="button" class="btn btn-outline-primary gen-quick-btn-expires" data-value="30">30天</button>
                                        </div>
                                        <button type="button" id="gen_linkButton" class="btn btn-success ms-2" data-linked="true">联动</button>
                                    </div>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="gen_count" class="form-label">生成数量:</label>
                                    <input type="number" id="gen_count" class="form-control" value="1" min="1" max="1000">
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="gen_download" class="form-label">下载链接:</label>
                                    <textarea id="gen_download" class="form-control" rows="2">夸克网盘: https://pan.quark.cn/s/942adad16049
百度网盘: https://pan.baidu.com/s/1_t3Q5cQdh6EFafOruyBFqg?pwd=jyru</textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 进度条 -->
                        <div class="progress mb-3" style="height: 4px; display: none;" id="gen_progressContainer">
                            <div class="progress-bar bg-primary" id="gen_progressBar" role="progressbar" style="width: 0%"></div>
                        </div>
                        
                        <!-- 按钮区域 -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <button id="gen_generateBtn" class="btn btn-primary me-2">生成卡密</button>
                                <button id="gen_copyBtn" class="btn btn-success me-2" disabled>复制信息</button>
                                <button id="gen_clearBtn" class="btn btn-secondary me-2" disabled>清空日志</button>
                                <button id="gen_downloadBtn" class="btn btn-info me-2" disabled>下载记录</button>
                                <button id="gen_adminModeBtn" class="btn btn-dark me-2" disabled>管理模式</button>
                            </div>
                        </div>
                        
                        <!-- 结果显示区域 -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="gen_result" class="form-label">生成结果:</label>
                                    <textarea id="gen_result" class="form-control" rows="12" readonly placeholder="生成的卡密将显示在这里..."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- API Key查询选项卡 -->
                    <div class="tab-pane fade" id="api-key-content" role="tabpanel" aria-labelledby="api-key-tab">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="input-group">
                                    <span class="input-group-text">API Key:</span>
                                    <input type="text" id="apiKeyInput" class="form-control" placeholder="输入API Key进行查询">
                                    <button id="apiKeyQueryBtn" class="btn btn-primary">查询</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">基本信息</h5>
                                    </div>
                                    <div class="card-body p-0">
                                        <table class="table table-bordered table-hover mb-0" id="apiInfoTable">
                                            <thead>
                                                <tr>
                                                    <th width="30%">属性</th>
                                                    <th width="70%">值</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- 数据将通过JavaScript动态填充 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12 d-flex justify-content-end">
                                <button id="manageThisKeyBtn" class="btn btn-success">管理此API Key</button>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">使用记录</h5>
                                    </div>
                                    <div class="card-body p-0">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-hover mb-0" id="usageTable">
                                                <thead>
                                                    <tr>
                                                        <th>时间</th>
                                                        <th>Cookie</th>
                                                        <th>客户端IP</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- 数据将通过JavaScript动态填充 -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 分页控件 -->
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <span>每页显示</span>
                                        <select id="apiPageSize" class="form-select form-select-sm d-inline-block mx-2" style="width: auto;">
                                            <option value="20">20</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                        <span>条</span>
                                    </div>
                                    <div>
                                        <button id="apiPrevBtn" class="btn btn-sm btn-outline-primary me-2">
                                            <i class="bi bi-chevron-left"></i>
                                        </button>
                                        <span>
                                            <input type="number" id="apiCurrentPage" class="form-control form-control-sm d-inline-block text-center" style="width: 60px;" value="1" min="1">
                                            <span id="apiTotalPages">/ 1</span>
                                        </span>
                                        <button id="apiNextBtn" class="btn btn-sm btn-outline-primary ms-2">
                                            <i class="bi bi-chevron-right"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Cookie查询选项卡 -->
                    <div class="tab-pane fade" id="cookie-content" role="tabpanel" aria-labelledby="cookie-tab">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="input-group">
                                    <span class="input-group-text">Cookie:</span>
                                    <input type="text" id="cookieInput" class="form-control" placeholder="输入Cookie进行查询">
                                    <button id="cookieQueryBtn" class="btn btn-primary">查询</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">使用记录</h5>
                                    </div>
                                    <div class="card-body p-0">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-hover mb-0" id="cookieUsageTable">
                                                <thead>
                                                    <tr>
                                                        <th>使用时间</th>
                                                        <th>API Key</th>
                                                        <th>客户端IP</th>
                                                        <th>操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- 数据将通过JavaScript动态填充 -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 分页控件 -->
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <span>每页显示</span>
                                        <select id="cookiePageSize" class="form-select form-select-sm d-inline-block mx-2" style="width: auto;">
                                            <option value="20">20</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                        <span>条</span>
                                    </div>
                                    <div>
                                        <button id="cookiePrevBtn" class="btn btn-sm btn-outline-primary me-2">
                                            <i class="bi bi-chevron-left"></i>
                                        </button>
                                        <span>
                                            <input type="number" id="cookieCurrentPage" class="form-control form-control-sm d-inline-block text-center" style="width: 60px;" value="1" min="1">
                                            <span id="cookieTotalPages">/ 1</span>
                                        </span>
                                        <button id="cookieNextBtn" class="btn btn-sm btn-outline-primary ms-2">
                                            <i class="bi bi-chevron-right"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- API Key管理选项卡 -->
                    <div class="tab-pane fade" id="manage-content" role="tabpanel" aria-labelledby="manage-tab">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="input-group">
                                    <span class="input-group-text">API Key:</span>
                                    <input type="text" id="manageKeyInput" class="form-control" placeholder="输入要管理的API Key">
                                    <button id="manageKeyQueryBtn" class="btn btn-primary">查询</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">当前信息</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>总配额:</strong> <span id="infoTotalQuota">--</span></p>
                                                <p><strong>剩余配额:</strong> <span id="infoRemaining">--</span></p>
                                                <p><strong>每日限制:</strong> <span id="infoDailyLimit">--</span></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>创建时间:</strong> <span id="infoCreatedAt">--</span></p>
                                                <p><strong>激活时间:</strong> <span id="infoFirstUsedAt">--</span></p>
                                                <p><strong>过期时间:</strong> <span id="infoExpiresAt">--</span></p>
                                                <p><strong>状态:</strong> <span id="infoIsActive">--</span></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">修改选项</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label class="form-label">增加配额:</label>
                                                <div class="input-group">
                                                    <input type="number" id="addQuotaInput" class="form-control" placeholder="输入要增加的配额" min="0">
                                                    <button id="addQuotaBtn" class="btn btn-primary">增加配额</button>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">每日限制:</label>
                                                <div class="input-group">
                                                    <input type="number" id="dailyLimitInput" class="form-control" placeholder="输入新的每日限制" min="0">
                                                    <button id="setDailyLimitBtn" class="btn btn-primary">设置限制</button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label class="form-label">延长有效期:</label>
                                                <div class="input-group">
                                                    <input type="number" id="extendDaysInput" class="form-control" placeholder="天" min="0">
                                                    <span class="input-group-text">天</span>
                                                    <input type="number" id="extendHoursInput" class="form-control" placeholder="小时" min="0">
                                                    <span class="input-group-text">时</span>
                                                    <input type="number" id="extendMinutesInput" class="form-control" placeholder="分钟" min="0">
                                                    <span class="input-group-text">分</span>
                                                    <button id="extendExpiryBtn" class="btn btn-primary">延长有效期</button>
                                                </div>
                                                <small class="form-text text-muted">可以单独设置天、小时、分钟，或组合使用</small>
                                                <div id="extendPreview" class="mt-2 text-info" style="display: none;">
                                                    <small><strong>预览:</strong> <span id="extendPreviewText"></span></small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">状态控制:</label>
                                                <div class="d-grid">
                                                    <button id="toggleActiveBtn" class="btn btn-warning">启用/禁用</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer text-muted">
                <div id="statusMessage"></div>
            </div>
        </div>
    </div>
    
    <!-- 提示框模态窗口 -->
    <!-- 已移除模态窗口，改用Toast通知 -->
    
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js', _external=True) }}"></script>
    <script src="{{ url_for('static', filename='js/query.js', _external=True) }}"></script>
    <script src="{{ url_for('static', filename='js/generator.js', _external=True) }}"></script>
    
    <!-- 调试信息 -->
    <script>
        console.log("页面加载完成");
        console.log("当前URL:", window.location.href);
        console.log("当前路径:", window.location.pathname);
        console.log("当前主机:", window.location.host);
        
        // 检查Bootstrap是否加载
        if (typeof bootstrap !== 'undefined') {
            console.log("Bootstrap已成功加载");
        } else {
            console.error("Bootstrap未加载，尝试手动加载");
            // 尝试手动加载Bootstrap
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js';
            script.onload = function() {
                console.log("Bootstrap已手动加载成功");
                // 初始化标签页
                const triggerTabList = document.querySelectorAll('#queryTabs button');
                triggerTabList.forEach(triggerEl => {
                    const tabTrigger = new bootstrap.Tab(triggerEl);
                    triggerEl.addEventListener('click', event => {
                        event.preventDefault();
                        tabTrigger.show();
                    });
                });
            };
            document.head.appendChild(script);
        }
    </script>
</body>
</html> 